using UnityEngine;
using Unity.Netcode;

public class TomrukController : NetworkBehaviour
{
    [Head<PERSON>("Hareket Ayarları")]
    [Tooltip("<PERSON><PERSON><PERSON><PERSON> ne kadar hızlı yuvarlana<PERSON>ğını belirler.")]
    [SerializeField] private float yuvarlanmaKuvveti = 10f;

    [Tooltip("Tomruğun hangi yöne gideceğini buradan ayarlayabilirsin. Örneğin Z ekseninde ileri için (0, 0, 1) yaz.")]
    [SerializeField] private Vector3 hareketYonu = new Vector3(0, 0, 1);

    [Header("Tag Ayarları")]
    [Tooltip("Tomruğu yok edecek objelerin tag'i")]
    [SerializeField] private string yokEdiciTag = "YokEdici";

    private Rigidbody rb;
    private Vector3 normalizedDirection; // Performans için cache'liyoruz
    private NetworkObject networkObject;

    public override void OnNetworkSpawn()
    {
        // NetworkObject referansını al
        networkObject = GetComponent<NetworkObject>();

        // Rigidbody kontrolü
        rb = GetComponent<Rigidbody>();
        if (rb == null)
        {
            Debug.LogError("TomrukController: Rigidbody komponenti bulunamadı!");
            enabled = false;
            return;
        }

        // Hareket yönünü normalize edip cache'liyoruz
        normalizedDirection = hareketYonu.normalized;
    }

    void FixedUpdate()
    {
        // Sadece server hareket hesaplamalarını yapar
        if (!IsServer) return;

        // Cache'lenmiş normalized direction kullanıyoruz
        rb.AddForce(normalizedDirection * yuvarlanmaKuvveti, ForceMode.Acceleration);
    }

    /// <summary>
    /// Çarpışma anında çalışan fonksiyon (Fiziksel çarpışma için)
    /// </summary>
    /// <param name="collision">Çarpışma bilgisi</param>
    private void OnCollisionEnter(Collision collision)
    {
        // Sadece server çarpışma kontrolü yapar
        if (!IsServer) return;

        // Çarptığımız objenin etiketini kontrol ediyoruz
        if (collision.gameObject.CompareTag(yokEdiciTag))
        {
            // Network objesini yok et
            YokEtRpc();
        }
    }

    /// <summary>
    /// Trigger çarpışması için (Eğer YokEdici obje Trigger ise)
    /// </summary>
    /// <param name="other">Trigger olan collider</param>
    private void OnTriggerEnter(Collider other)
    {
        // Sadece server çarpışma kontrolü yapar
        if (!IsServer) return;

        // Trigger olan objenin etiketini kontrol ediyoruz
        if (other.gameObject.CompareTag(yokEdiciTag))
        {
            // Network objesini yok et
            YokEtRpc();
        }
    }

    /// <summary>
    /// Tomruğu network üzerinden yok eder
    /// </summary>
    [Rpc(SendTo.Server)]
    private void YokEtRpc()
    {
        if (IsServer && networkObject != null)
        {
            // NetworkObject'i despawn et
            networkObject.Despawn(true);
        }
    }

    /// <summary>
    /// Spawner tarafından ayarlandığında çağrılır
    /// </summary>
    public void SpawnerTarafindanAyarlandi()
    {
        // Hareket yönünü yeniden hesapla
        if (Application.isPlaying)
        {
            normalizedDirection = hareketYonu.normalized;
        }
    }

    /// <summary>
    /// Inspector'da hareket yönü değiştirildiğinde çalışır
    /// </summary>
    private void OnValidate()
    {
        if (Application.isPlaying)
        {
            normalizedDirection = hareketYonu.normalized;
        }
    }
}