using UnityEngine;

public class TomrukController : MonoBehaviour
{
    [Header("Hareket Ayarları")]
    [Toolt<PERSON>("<PERSON><PERSON><PERSON><PERSON> ne kadar hızlı yuvarlana<PERSON>ğını belirler.")]
    [SerializeField] private float yuvarlanmaKuvveti = 10f;

    [Tooltip("Tomruğun hangi yöne gideceğini buradan ayarlayabilirsin. Örneğin Z ekseninde ileri için (0, 0, 1) yaz.")]
    [SerializeField] private Vector3 hareketYonu = new Vector3(0, 0, 1);

    [Header("Tag Ayarları")]
    [Tooltip("Tomruğu yok edecek objelerin tag'i")]
    [SerializeField] private string yokEdiciTag = "YokEdici";

    private Rigidbody rb;
    private Vector3 normalizedDirection; // Performans için cache'liyoruz

    void Start()
    {
        // Rigidbody kontrolü
        rb = GetComponent<Rigidbody>();
        if (rb == null)
        {
            Debug.LogError("TomrukController: Rigidbody komponenti bulunamadı!");
            enabled = false;
            return;
        }

        // Hareket yönünü normalize edip cache'liyoruz
        normalizedDirection = hareketYonu.normalized;
    }

    void FixedUpdate()
    {
        // Cache'lenmiş normalized direction kullanıyoruz
 
 
        rb.AddForce(normalizedDirection * yuvarlanmaKuvveti, ForceMode.Acceleration);
    }

    /// <summary>
    /// Çarpışma anında çalışan fonksiyon (Fiziksel çarpışma için)
    /// </summary>
    /// <param name="collision">Çarpışma bilgisi</param>
    private void OnCollisionEnter(Collision collision)
    {
        // Çarptığımız objenin etiketini kontrol ediyoruz
        if (collision.gameObject.CompareTag(yokEdiciTag))
        {
            // Bu objeyi (tomruğu) yok et
            Destroy(gameObject);
        }
    }

    /// <summary>
    /// Trigger çarpışması için (Eğer YokEdici obje Trigger ise)
    /// </summary>
    /// <param name="other">Trigger olan collider</param>
    private void OnTriggerEnter(Collider other)
    {
        // Trigger olan objenin etiketini kontrol ediyoruz
        if (other.gameObject.CompareTag(yokEdiciTag))
        {
            // Bu objeyi (tomruğu) yok et
            Destroy(gameObject);
        }
    }

    /// <summary>
    /// Inspector'da hareket yönü değiştirildiğinde çalışır
    /// </summary>
    private void OnValidate()
    {
        if (Application.isPlaying)
        {
            normalizedDirection = hareketYonu.normalized;
        }
    }
}